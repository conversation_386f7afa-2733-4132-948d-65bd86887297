const t=(t,e,o=[])=>{const r=Object.getOwnPropertyDescriptors(e);for(let t of o)delete r[t];Object.defineProperties(t,r)},e=(t,o=[t])=>{const r=Object.getPrototypeOf(t);return null===r?o:e(r,[...o,r])},o=(o,r,n=[])=>{var l;const i=null!==(l=((...t)=>{if(0===t.length)return;let o;const r=t.map((t=>e(t)));for(;r.every((t=>t.length>0));){const t=r.map((t=>t.pop())),e=t[0];if(!t.every((t=>t===e)))break;o=e}return o})(...o))&&void 0!==l?l:Object.prototype,c=Object.create(i),s=e(i);for(let r of o){let o=e(r);for(let e=o.length-1;e>=0;e--){let r=o[e];-1===s.indexOf(r)&&(t(c,r,["constructor",...n]),s.push(r))}}return c.constructor=r,c},r=t=>t.filter(((e,o)=>t.indexOf(e)==o)),n=(t,o)=>{const r=o.map((t=>e(t)));let n=0,l=!0;for(;l;){l=!1;for(let e=o.length-1;e>=0;e--){const o=r[e][n];if(null!=o&&(l=!0,null!=Object.getOwnPropertyDescriptor(o,t)))return r[e][0]}n++}},l=(t,e=Object.prototype)=>new Proxy({},{getPrototypeOf:()=>e,setPrototypeOf(){throw Error("Cannot set prototype of Proxies created by ts-mixer")},getOwnPropertyDescriptor:(e,o)=>Object.getOwnPropertyDescriptor(n(o,t)||{},o),defineProperty(){throw new Error("Cannot define new properties on Proxies created by ts-mixer")},has:(o,r)=>void 0!==n(r,t)||void 0!==e[r],get:(o,r)=>(n(r,t)||e)[r],set(e,o,r){const l=n(o,t);if(void 0===l)throw new Error("Cannot set new properties on Proxies created by ts-mixer");return l[o]=r,!0},deleteProperty(){throw new Error("Cannot delete properties on Proxies created by ts-mixer")},ownKeys:()=>t.map(Object.getOwnPropertyNames).reduce(((t,e)=>e.concat(t.filter((t=>e.indexOf(t)<0)))))}),i={initFunction:null,staticsStrategy:"copy",prototypeStrategy:"copy",decoratorInheritance:"deep"},c=new WeakMap,s=t=>c.get(t),p=(t,o)=>{if(t instanceof o)return!0;const r=t.constructor,n=new Set;let l=new Set;for(l.add(r);l.size>0;){if(l.has(o))return!0;l.forEach((t=>n.add(t)));const t=new Set;l.forEach((o=>{var r;const i=null!==(r=c.get(o))&&void 0!==r?r:e(o.prototype).map((t=>t.constructor)).filter((t=>null!==t));i&&i.forEach((e=>{n.has(e)||l.has(e)||t.add(e)}))})),l=t}return!1},u=(t,e)=>{var o,n;const l=r([...Object.getOwnPropertyNames(t),...Object.getOwnPropertyNames(e)]),i={};for(let c of l)i[c]=r([...null!==(o=null==t?void 0:t[c])&&void 0!==o?o:[],...null!==(n=null==e?void 0:e[c])&&void 0!==n?n:[]]);return i},a=(t,e)=>{var o,r,n,l;return{property:u(null!==(o=null==t?void 0:t.property)&&void 0!==o?o:{},null!==(r=null==e?void 0:e.property)&&void 0!==r?r:{}),method:u(null!==(n=null==t?void 0:t.method)&&void 0!==n?n:{},null!==(l=null==e?void 0:e.method)&&void 0!==l?l:{})}},d=(t,e)=>{var o,n,l,i,c,s;return{class:r([...null!==(o=null==t?void 0:t.class)&&void 0!==o?o:[],...null!==(n=null==e?void 0:e.class)&&void 0!==n?n:[]]),static:a(null!==(l=null==t?void 0:t.static)&&void 0!==l?l:{},null!==(i=null==e?void 0:e.static)&&void 0!==i?i:{}),instance:a(null!==(c=null==t?void 0:t.instance)&&void 0!==c?c:{},null!==(s=null==e?void 0:e.instance)&&void 0!==s?s:{})}},f=new Map,v=(...t)=>{const o=((...t)=>{var o;const r=new Set,n=new Set([...t]);for(;n.size>0;)for(let t of n){const l=[...e(t.prototype).map((t=>t.constructor)),...null!==(o=s(t))&&void 0!==o?o:[]].filter((t=>!r.has(t)));for(let t of l)n.add(t);r.add(t),n.delete(t)}return[...r]})(...t).map((t=>f.get(t))).filter((t=>!!t));return 0==o.length?{}:1==o.length?o[0]:o.reduce(((t,e)=>d(t,e)))},y=t=>{let e=f.get(t);return e||(e={},f.set(t,e)),e},h=t=>(...e)=>1===e.length?(t=>e=>{const o=y(e);let r=o.class;return r||(r=[],o.class=r),r.push(t),t(e)})(t)(e[0]):(t=>(e,o,...r)=>{var n,l,i;const c="function"==typeof e?"static":"instance",s="function"==typeof e[o]?"method":"property",p="static"===c?e:e.constructor,u=y(p),a=null!==(n=null==u?void 0:u[c])&&void 0!==n?n:{};u[c]=a;let d=null!==(l=null==a?void 0:a[s])&&void 0!==l?l:{};a[s]=d;let f=null!==(i=null==d?void 0:d[o])&&void 0!==i?i:[];return d[o]=f,f.push(t),t(e,o,...r)})(t)(...e);function O(...e){var r,n,s;const p=e.map((t=>t.prototype)),u=i.initFunction;if(null!==u){const t=p.map((t=>t[u])).filter((t=>"function"==typeof t)),e={[u]:function(...e){for(let o of t)o.apply(this,e)}};p.push(e)}function a(...o){for(const r of e)t(this,new r(...o));null!==u&&"function"==typeof this[u]&&this[u].apply(this,o)}var f,h;a.prototype="copy"===i.prototypeStrategy?o(p,a):(f=p,h=a,l([...f,{constructor:h}])),Object.setPrototypeOf(a,"copy"===i.staticsStrategy?o(e,null,["prototype"]):l(e,Function.prototype));let O=a;if("none"!==i.decoratorInheritance){const t="deep"===i.decoratorInheritance?v(...e):((...t)=>{const e=t.map((t=>y(t)));return 0===e.length?{}:1===e.length?e[0]:e.reduce(((t,e)=>d(t,e)))})(...e);for(let e of null!==(r=null==t?void 0:t.class)&&void 0!==r?r:[]){const t=e(O);t&&(O=t)}g(null!==(n=null==t?void 0:t.static)&&void 0!==n?n:{},O),g(null!==(s=null==t?void 0:t.instance)&&void 0!==s?s:{},O.prototype)}var w,m;return w=O,m=e,c.set(w,m),O}const g=(t,e)=>{const o=t.property,r=t.method;if(o)for(let t in o)for(let r of o[t])r(e,t);if(r)for(let t in r)for(let o of r[t])o(e,t,Object.getOwnPropertyDescriptor(e,t))},w=(...t)=>e=>{const o=O(...t.concat([e]));return Object.defineProperty(o,"name",{value:e.name,writable:!1}),o};export{O as Mixin,h as decorate,p as hasMixin,w as mix,i as settings};
