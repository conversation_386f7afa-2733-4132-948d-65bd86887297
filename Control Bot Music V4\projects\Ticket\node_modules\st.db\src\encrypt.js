"use strict";
let crypto = require("crypto")
let encrypt = function() {
  function t(r) {
    let i = this;
    this.deriveKey = function(r, i) {
      let n = Object.assign({}, t.defaultDeriveKeyOpts, i)
      let s = n.salt
      let o = n.iterations
      let a = n.digest
      return crypto.pbkdf2Sync(r, s, o, 32, a);
    }
    this.encryptString = (t, r) => {
      let n = i.deriveKey(r, i._deriveKeyOptions)
      let s = crypto.randomBytes(16)
      let o = crypto.createCipheriv("aes-256-gcm", n, s)
      let a = o.update(t.toString(), "utf8", "base64");
      a += o.final("base64"); var f = Buffer.from(a).toString("hex");
      return s.toString("hex") + ":" + f;
    }
    this.decryptString = (t, r) => {
      let n = i.deriveKey(r, i._deriveKeyOptions)
      let s = t.toString().split(":"); if (2 !== s.length) throw new Error("[ST.db] - Incorrect format for encrypted string: " + t);
      let o = s[0]
      let a = s[1]
      let f = Buffer.from(o, "hex")
      let u = Buffer.from(a, "hex").toString();
      return crypto.createDecipheriv("aes-256-gcm", n, f).update(u, "base64").toString();
    }
    this.isEncrypt = (t, r) => {
      try {
        let n = i.deriveKey(r, i._deriveKeyOptions)
        let s = t.toString().split(":"); if (2 !== s.length) return false
        let o = s[0]
        let a = s[1]
        let f = Buffer.from(o, "hex")
        let u = Buffer.from(a, "hex").toString();
        return true
      } catch (e) {
        return false
      }
    }, r && (this._deriveKeyOptions = r);
  } return t.defaultDeriveKeyOpts = { salt: "s41t", iterations: 1, digest: "sha512" }, t;
}();
module.exports = encrypt;