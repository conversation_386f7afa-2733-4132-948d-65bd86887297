const fs = require('fs');
const path = require('path');
const config = require('../config.json');

// تحميل ملفات الترجمة
const locales = {};
const localesPath = path.join(__dirname, '../locales');
const localeFiles = fs.readdirSync(localesPath).filter(file => file.endsWith('.json'));

for (const file of localeFiles) {
  const localeName = file.split('.')[0];
  const filePath = path.join(localesPath, file);
  const localeData = require(filePath);
  locales[localeName] = localeData;
}

// الحصول على ترجمة بناءً على المسار
function getTranslation(locale, path) {
  if (!locales[locale]) {
    locale = 'en'; // استخدام الإنجليزية كلغة افتراضية
  }

  const keys = path.split('.');

  // محاولة الحصول على الترجمة من اللغة المحددة
  let result = getNestedTranslation(locales[locale], keys);

  // إذا لم يتم العثور على الترجمة، استخدم الإنجليزية
  if (result === undefined && locale !== 'en') {
    result = getNestedTranslation(locales['en'], keys);
  }

  // إذا لم يتم العثور على الترجمة حتى في الإنجليزية، أرجع المسار
  if (result === undefined) {
    console.warn(`Translation not found for path: ${path}`);
    return path;
  }

  return result;
}

// دالة مساعدة للحصول على قيمة متداخلة
function getNestedTranslation(obj, keys) {
  let current = obj;

  for (const key of keys) {
    if (current === undefined || current[key] === undefined) {
      return undefined;
    }
    current = current[key];
  }

  return current;
}

// استبدال المتغيرات في النص
function replaceVariables(text, variables) {
  if (typeof text !== 'string') {
    return text;
  }

  let result = text;

  if (variables) {
    for (const [key, value] of Object.entries(variables)) {
      result = result.replace(new RegExp(`{${key}}`, 'g'), value);
    }
  }

  return result;
}

// الحصول على ترجمة مع استبدال المتغيرات
async function translate(guildId, path, variables) {
  try {
    // الحصول على لغة السيرفر
    let locale = config.defaultLanguage; // استخدام اللغة الافتراضية من الإعدادات

    if (guildId) {
      // استيراد guildService هنا لتجنب دائرة الاستيراد
      const { guildService } = require('../services');
      const guild = await guildService.getGuildSettings(guildId);
      if (guild && guild.language) {
        locale = guild.language;
      }
    }

    // الحصول على الترجمة
    const translation = getTranslation(locale, path);

    // استبدال المتغيرات
    return replaceVariables(translation, variables);
  } catch (error) {
    console.error('Error translating:', error);
    return path; // إرجاع المسار في حالة حدوث خطأ
  }
}

// تغيير لغة السيرفر
async function setGuildLanguage(guildId, language) {
  try {
    if (!locales[language]) {
      return false; // اللغة غير مدعومة
    }

    // استيراد guildService هنا لتجنب دائرة الاستيراد
    const { guildService } = require('../services');
    await guildService.updateGuildSettings(guildId, { language });
    return true;
  } catch (error) {
    console.error('Error setting guild language:', error);
    return false;
  }
}

// الحصول على اللغات المدعومة
function getSupportedLanguages() {
  return Object.keys(locales);
}

module.exports = {
  translate,
  setGuildLanguage,
  getSupportedLanguages
};
