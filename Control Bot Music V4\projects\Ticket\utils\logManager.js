const { EmbedBuilder } = require('discord.js');
const { panelService, ticketService } = require('../services');

/**
 * إرسال سجل إلى قناة النسخ
 * @param {Object} client - عميل Discord
 * @param {String} guildId - معرف السيرفر
 * @param {String} panelId - معرف اللوحة
 * @param {String} type - نوع السجل (ticket_created, ticket_closed, ticket_deleted, member_added, member_removed)
 * @param {Object} data - بيانات إضافية للسجل
 */
async function sendLog(client, guildId, panelId, type, data = {}) {
  try {
    // الحصول على بيانات اللوحة
    const panel = await panelService.getPanelById(panelId);
    if (!panel || !panel.transcriptChannel) return;

    // الحصول على قناة النسخ
    const transcriptChannel = await client.channels.fetch(panel.transcriptChannel).catch(() => null);
    if (!transcriptChannel) return;

    // الحصول على الترجمات
    const logTitle = await client.translate(guildId, `ticket.log.${type}`);
    
    // إنشاء سجل مناسب حسب النوع
    const embed = new EmbedBuilder()
      .setColor(getColorByLogType(type))
      .setTitle(logTitle)
      .setTimestamp();

    // إضافة حقول مختلفة حسب نوع السجل
    switch (type) {
      case 'ticket_created':
        if (data.user) {
          const createdByText = await client.translate(guildId, 'ticket.log.created_by');
          embed.addFields({ name: createdByText, value: `<@${data.user.id}>` });
        }
        if (data.channel) {
          embed.addFields({ name: 'القناة', value: `<#${data.channel.id}>` });
        }
        break;

      case 'ticket_closed':
        if (data.user) {
          const closedByText = await client.translate(guildId, 'ticket.log.closed_by');
          embed.addFields({ name: closedByText, value: `<@${data.user.id}>` });
        }
        if (data.channel) {
          embed.addFields({ name: 'القناة', value: `<#${data.channel.id}>` });
        }
        break;

      case 'ticket_deleted':
        if (data.user) {
          const deletedByText = await client.translate(guildId, 'ticket.log.deleted_by');
          embed.addFields({ name: deletedByText, value: `<@${data.user.id}>` });
        }
        if (data.channelName) {
          embed.addFields({ name: 'اسم القناة', value: data.channelName });
        }
        break;

      case 'member_added':
        if (data.user) {
          const addedByText = await client.translate(guildId, 'ticket.log.added_by');
          embed.addFields({ name: addedByText, value: `<@${data.user.id}>` });
        }
        if (data.member) {
          embed.addFields({ name: 'العضو المضاف', value: `<@${data.member.id}>` });
        }
        if (data.channel) {
          embed.addFields({ name: 'القناة', value: `<#${data.channel.id}>` });
        }
        break;

      case 'member_removed':
        if (data.user) {
          const removedByText = await client.translate(guildId, 'ticket.log.removed_by');
          embed.addFields({ name: removedByText, value: `<@${data.user.id}>` });
        }
        if (data.member) {
          embed.addFields({ name: 'العضو المزال', value: `<@${data.member.id}>` });
        }
        if (data.channel) {
          embed.addFields({ name: 'القناة', value: `<#${data.channel.id}>` });
        }
        break;
    }

    // إرسال السجل إلى قناة النسخ
    await transcriptChannel.send({ embeds: [embed] });
  } catch (error) {
    console.error('Error sending log:', error);
  }
}

/**
 * الحصول على لون مناسب حسب نوع السجل
 * @param {String} type - نوع السجل
 * @returns {String} - كود اللون
 */
function getColorByLogType(type) {
  switch (type) {
    case 'ticket_created':
      return '#00ff00'; // أخضر
    case 'ticket_closed':
      return '#ffaa00'; // برتقالي
    case 'ticket_deleted':
      return '#ff0000'; // أحمر
    case 'member_added':
      return '#00aaff'; // أزرق فاتح
    case 'member_removed':
      return '#aa00ff'; // أرجواني
    default:
      return '#ffffff'; // أبيض
  }
}

module.exports = {
  sendLog
};
