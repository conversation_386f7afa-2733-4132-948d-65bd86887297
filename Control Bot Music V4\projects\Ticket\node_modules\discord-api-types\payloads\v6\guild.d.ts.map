{"version": 3, "file": "guild.d.ts", "sourceRoot": "", "sources": ["guild.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,WAAW,CAAC;AAC5C,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,SAAS,CAAC;AACxC,OAAO,KAAK,EAAE,qBAAqB,EAAE,MAAM,WAAW,CAAC;AACvD,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AAC7C,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,QAAQ,CAAC;AACtC,OAAO,KAAK,EAAE,iBAAiB,EAAE,MAAM,SAAS,CAAC;AAEjD;;;;GAIG;AACH,MAAM,WAAW,mBAAmB;IACnC,EAAE,EAAE,MAAM,CAAC;IACX,WAAW,EAAE,OAAO,CAAC;CACrB;AAED;;GAEG;AACH,MAAM,WAAW,eAAgB,SAAQ,IAAI,CAAC,mBAAmB,EAAE,aAAa,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,gBAAgB,CAAC;IAClH,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC;IACpB,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;IACtB,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACvB,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAC5B,QAAQ,CAAC,EAAE,YAAY,EAAE,CAAC;IAC1B,kBAAkB,CAAC,EAAE,sBAAsB,CAAC;IAC5C,eAAe,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAChC,WAAW,CAAC,EAAE,OAAO,CAAC;CACtB;AAED;;GAEG;AACH,MAAM,WAAW,QAAS,SAAQ,eAAe;IAChD,gBAAgB,EAAE,MAAM,GAAG,IAAI,CAAC;IAChC,KAAK,CAAC,EAAE,OAAO,CAAC;IAChB,QAAQ,EAAE,MAAM,CAAC;IACjB;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,MAAM,EAAE,MAAM,CAAC;IACf,cAAc,EAAE,MAAM,GAAG,IAAI,CAAC;IAC9B,WAAW,EAAE,MAAM,CAAC;IACpB;;OAEG;IACH,aAAa,CAAC,EAAE,OAAO,CAAC;IACxB;;OAEG;IACH,gBAAgB,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACjC,kBAAkB,EAAE,sBAAsB,CAAC;IAC3C,6BAA6B,EAAE,gCAAgC,CAAC;IAChE,uBAAuB,EAAE,0BAA0B,CAAC;IACpD,KAAK,EAAE,OAAO,EAAE,CAAC;IACjB,MAAM,EAAE,QAAQ,EAAE,CAAC;IACnB,QAAQ,EAAE,YAAY,EAAE,CAAC;IACzB,SAAS,EAAE,aAAa,CAAC;IACzB,cAAc,EAAE,MAAM,GAAG,IAAI,CAAC;IAC9B,cAAc,CAAC,EAAE,OAAO,CAAC;IACzB,iBAAiB,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAClC,iBAAiB,EAAE,MAAM,GAAG,IAAI,CAAC;IACjC,oBAAoB,EAAE,uBAAuB,CAAC;IAC9C,gBAAgB,EAAE,MAAM,GAAG,IAAI,CAAC;IAChC,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,KAAK,CAAC,EAAE,OAAO,CAAC;IAChB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,YAAY,CAAC,EAAE,IAAI,CAAC,iBAAiB,EAAE,UAAU,CAAC,EAAE,CAAC;IACrD,OAAO,CAAC,EAAE,cAAc,EAAE,CAAC;IAC3B,QAAQ,CAAC,EAAE,UAAU,EAAE,CAAC;IACxB,SAAS,CAAC,EAAE,qBAAqB,EAAE,CAAC;IACpC,aAAa,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAC9B,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,eAAe,EAAE,MAAM,GAAG,IAAI,CAAC;IAC/B,WAAW,EAAE,MAAM,GAAG,IAAI,CAAC;IAC3B,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;IACtB,YAAY,EAAE,gBAAgB,CAAC;IAC/B,0BAA0B,CAAC,EAAE,MAAM,CAAC;IACpC,gBAAgB,EAAE,MAAM,CAAC;IACzB,yBAAyB,EAAE,MAAM,GAAG,IAAI,CAAC;IACzC,uBAAuB,CAAC,EAAE,MAAM,CAAC;IACjC;;OAEG;IACH,wBAAwB,CAAC,EAAE,MAAM,CAAC;IAClC;;OAEG;IACH,0BAA0B,CAAC,EAAE,MAAM,CAAC;IACpC,cAAc,CAAC,EAAE,qBAAqB,CAAC;CACvC;AAED;;;;GAIG;AACH,oBAAY,gCAAgC;IAC3C,YAAY,IAAA;IACZ,aAAa,IAAA;CACb;AAED;;;;GAIG;AACH,oBAAY,0BAA0B;IACrC,QAAQ,IAAA;IACR,qBAAqB,IAAA;IACrB,WAAW,IAAA;CACX;AAED;;;;GAIG;AACH,oBAAY,aAAa;IACxB,IAAI,IAAA;IACJ,QAAQ,IAAA;CACR;AAED;;;;GAIG;AACH,oBAAY,sBAAsB;IACjC,IAAI,IAAA;IACJ,GAAG,IAAA;IACH,MAAM,IAAA;IACN,IAAI,IAAA;IACJ,SAAS,IAAA;CACT;AAED;;;;GAIG;AACH,oBAAY,gBAAgB;IAC3B,IAAI,IAAA;IACJ,MAAM,IAAA;IACN,MAAM,IAAA;IACN,MAAM,IAAA;CACN;AAED;;;;GAIG;AACH,oBAAY,uBAAuB;IAClC,2BAA2B,IAAS;IACpC,8BAA8B,IAAS;CACvC;AAED;;;;GAIG;AACH,oBAAY,YAAY;IACvB,aAAa,kBAAkB;IAC/B,MAAM,WAAW;IACjB,QAAQ,aAAa;IACrB,SAAS,cAAc;IACvB,YAAY,iBAAiB;IAC7B,UAAU,eAAe;IACzB,aAAa,kBAAkB;IAC/B,IAAI,SAAS;IACb,SAAS,cAAc;IACvB,aAAa,kBAAkB;IAC/B,UAAU,eAAe;IACzB,QAAQ,aAAa;IACrB,WAAW,gBAAgB;IAC3B,sBAAsB,2BAA2B;CACjD;AAED;;;;GAIG;AACH,MAAM,WAAW,eAAe;IAC/B,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC;IACpB,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;IACtB,gBAAgB,EAAE,MAAM,GAAG,IAAI,CAAC;IAChC,MAAM,EAAE,QAAQ,EAAE,CAAC;IACnB,QAAQ,EAAE,YAAY,EAAE,CAAC;IACzB,wBAAwB,EAAE,MAAM,CAAC;IACjC,0BAA0B,EAAE,MAAM,CAAC;CACnC;AAED;;;GAGG;AACH,MAAM,MAAM,cAAc,GAAG,sBAAsB,CAAC;AAEpD;;;;GAIG;AACH,MAAM,WAAW,sBAAsB;IACtC,OAAO,EAAE,OAAO,CAAC;IACjB,UAAU,EAAE,MAAM,GAAG,IAAI,CAAC;CAC1B;AAED;;;;GAIG;AACH,MAAM,WAAW,cAAc;IAC9B,IAAI,CAAC,EAAE,OAAO,CAAC;IACf,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC;IACpB,KAAK,EAAE,MAAM,EAAE,CAAC;IAChB,SAAS,EAAE,MAAM,CAAC;IAClB,aAAa,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAC9B,IAAI,EAAE,OAAO,CAAC;IACd,IAAI,EAAE,OAAO,CAAC;CACd;AAED;;;;GAIG;AACH,MAAM,WAAW,mBAAmB;IACnC,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,MAAM,CAAC;IACb,OAAO,EAAE,OAAO,CAAC;IACjB,OAAO,EAAE,OAAO,CAAC;IACjB,OAAO,EAAE,MAAM,CAAC;IAChB,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC3B,eAAe,EAAE,yBAAyB,CAAC;IAC3C,mBAAmB,EAAE,MAAM,CAAC;IAC5B,IAAI,CAAC,EAAE,OAAO,CAAC;IACf,OAAO,EAAE,qBAAqB,CAAC;IAC/B,SAAS,EAAE,MAAM,CAAC;IAClB,gBAAgB,EAAE,MAAM,CAAC;IACzB,OAAO,EAAE,OAAO,CAAC;IACjB,WAAW,CAAC,EAAE,8BAA8B,CAAC;CAC7C;AAED;;;;GAIG;AACH,oBAAY,yBAAyB;IACpC,UAAU,IAAA;IACV,IAAI,IAAA;CACJ;AAED;;;;GAIG;AACH,MAAM,WAAW,qBAAqB;IACrC,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,MAAM,CAAC;CACb;AAED;;;;GAIG;AACH,MAAM,WAAW,8BAA8B;IAC9C,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC;IACpB,WAAW,EAAE,MAAM,CAAC;IACpB,OAAO,EAAE,MAAM,CAAC;IAChB,GAAG,CAAC,EAAE,OAAO,CAAC;CACd;AAED;;;;GAIG;AACH,MAAM,WAAW,MAAM;IACtB,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;IACtB,IAAI,EAAE,OAAO,CAAC;CACd;AAED;;;;GAIG;AACH,oBAAY,gBAAgB;IAC3B,OAAO,YAAY;IACnB,OAAO,YAAY;IACnB,OAAO,YAAY;IACnB,OAAO,YAAY;IACnB,MAAM,WAAW;CACjB;AAED;;GAEG;AACH,MAAM,WAAW,qBAAqB;IACrC,WAAW,EAAE,MAAM,GAAG,IAAI,CAAC;IAC3B,gBAAgB,EAAE,4BAA4B,EAAE,CAAC;CACjD;AAED;;GAEG;AACH,MAAM,WAAW,4BAA4B;IAC5C,UAAU,EAAE,MAAM,CAAC;IACnB,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IACxB,UAAU,EAAE,MAAM,GAAG,IAAI,CAAC;CAC1B"}