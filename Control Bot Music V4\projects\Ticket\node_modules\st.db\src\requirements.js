"use strict";
const { access, writeFileSync, constants,statSync } = require("fs");
const path = require("path")
const http = require("http");

const createAPI = (path, all, debug) => {
  let PORT = process.env.PORT || 5000;
  let server = http.createServer(async (req, res) => {
    if (req.url === `/${path}` && req.method === "GET") {
      res.writeHead(200, { "Content-Type": "application/json" });
      res.write(JSON.stringify(all));
      res.end();
    }
  })
  server.listen(PORT, () => {
    if (debug == true) {
      let d = new Date();
      let h = addZero(d.getUTCHours());
      let m = addZero(d.getUTCMinutes());
      let s = addZero(d.getUTCSeconds());
      let time = h + ":" + m + ":" + s;
      console.log(`\u001b[33;1m[${time}][ST.db] - Server started on port: ${PORT}\u001b[0m`);
      console.log(`\u001b[33;1m[${time}][ST.db] - Your Database Path http://localhost:5000/${path}\u001b[0m`);
    }
  })
}

const file_exists = (path) => {
  try{
    statSync(`${path}`)
    return true
  }catch(e){
    return false
  }
}

const createFile = (path, isYML) => {
  if (isYML == true) {
    writeFileSync(path, "", null, 4)
  } else {
    writeFileSync(path, JSON.stringify([], null, 4))
  }
};

const pathResolve = (filePath, fileExtension,pathOutsideTheProject) => {
  if (filePath.startsWith(process.cwd())) filePath = filePath.replace(basePath, "");
  if (filePath.startsWith("./")) filePath = filePath.slice(2);
  if (filePath.startsWith("." + path.sep)) filePath = filePath.slice(1);
  if (!filePath.endsWith("." + fileExtension)) {
    filePath += `.${fileExtension}`;
  }
  return pathOutsideTheProject != true ? path.resolve(`./${filePath}`) : filePath
}


const debug = (text, status) => {
  if(status == 1){
    throw Error(`[ST.db] - ${text}`)
  } else {
    console.log(`\u001b[3${status || 2};1m[ST.db] - ${text}\u001b[0m`)
  }
}

module.exports = {
  createAPI, createFile, pathResolve, debug,file_exists
};