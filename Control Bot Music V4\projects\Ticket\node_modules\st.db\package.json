{"name": "st.db", "version": "4.0.7", "description": "It helps you to store files inside the project or outside the project in YML and JSON formats in shapes such as objects or maps", "keywords": ["st.db", "json", "yaml", "yml", "mongo", "db", "encryption", "encrypt", "st", "discord", "database", "quick.db", "object", "json", "map", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "dependencies": {"yaml": "^1.10.2"}, "engines": {"node": ">=14.0.0"}, "devDependencies": {}, "main": "index.js", "bin": "index.js", "scripts": {"start": "node index.js"}, "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "CC BY-NC-ND 4.0"}