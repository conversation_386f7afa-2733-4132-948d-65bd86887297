{"name": "new", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node .", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@discordjs/voice": "^0.16.0", "@distube/deezer": "^1.0.0", "@distube/soundcloud": "^1.3.0", "@distube/spotify": "^1.5.1", "axios": "^1.3.4", "canvas": "^3.1.0", "discord.js": "^14.8.0", "distube": "^4.0.4", "dotenv": "^16.0.3", "ffmpeg": "^0.0.4", "ffmpeg-static": "^4.4.1", "libsodium-wrappers": "^0.7.11", "mongoose": "^7.0.1", "ms": "^2.1.3", "node-fetch": "^3.3.1", "opusscript": "^0.0.8", "pm2": "^5.2.2", "pretty-ms": "^7.0.1", "st.db": "^7.2.10", "windows-cpu": "^1.1.0"}}