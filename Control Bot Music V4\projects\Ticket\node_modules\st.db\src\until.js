(function() {
  "use strict";
  const { createAPI, pathResolve, creationOptions, createFile, debug, file_exists } = require('./requirements.js');
  const { EventEmitter } = require('events');
  const YAML = require("yaml");
  const { writeFileSync, readFileSync, statSync, unlinkSync } = require("fs");
  const Crypto = require('./encrypt.js')

  class Database {
    /** 
    * It helps you to store files inside the project or outside the project in YML and JSON formats in shapes such as objects or maps
    * @param {Object} options
    */
    constructor(options) {
      if (typeof options == `object`) {
        this.inputPath = options.path
        this.pathOutsideTheProject = options.pathOutsideTheProject && options.pathOutsideTheProject == true ? true : false
        this.API = options.api ? options.api : options.API ? options.API : false
        this.debug = options.debug && options.debug == true ? true : false
        this.databaseInObject = options.databaseInObject && options.databaseInObject == true ? true : false
        if (options.encryption == true || options.encryption && options.encryption.password) this.encryption = true
        else this.encryption = false
        if (typeof options.encryption == "object" && options.encryption.password) this.encryptionPassword = options.encryption.password
        else this.encryptionPassword = "st.db";
      } else {
        this.inputPath = options
      }
      if (!this.inputPath) {
        this.inputPath = "data.json"
      }
      this.crypto = new Crypto({ digest: typeof options.encryption == "object" && options.encryption.digest ? options.encryption.digest : 'sha512' });
      this.fileExtension = this.inputPath.endsWith("yml") ? "yml" : this.inputPath.endsWith("yaml") ? "yaml": "json"
      this.isYML = this.fileExtension == "yml" || this.fileExtension == "yaml" ? true : false
      this.path = pathResolve(this.inputPath, this.fileExtension,this.pathOutsideTheProject)
      this.event = new EventEmitter();
      this.readyInDate = new Date();
      this.file_exists = file_exists(this.path)
      if (this.debug == true) {
        debug("Your Database is connected successfully", 2)
      }
      if(this.file_exists == true){
        this.size = this.cacheGet().size || 0
        this.cache = this.cacheGet() || new Map()
        if(this.API == true){
          createAPI(this.inputPath, [], this.isYML)
          if (this.debug == true) {
            debug("Now the api is now available with the extension data for the file", 3)
          }
        }
      } else {
        createFile(this.path, this.isYML)
        this.size = 0
        this.cache = new Map()
      }
    }
    /**
    * To listen to the available events
    * 
    * @exemple db.on(event, async(callback) => {})
    * @param {string} event The name of the event you want
    * @returns {Object}
    */
    on(event, callback) {
      this.event.on(event, callback);
      if (event == 'isReady') this.event.emit('isReady', this)
      return this;
    }
    /**
    * Check if it is a number or not
    * 
    * @param {number} val 
    * @returns {Boolean}
    */
    isNumeric(val) {
      return /^-?\d+$/.test(val);
    }
    /**
     * @returns ([])
     */
    cacheGet() {
      try {
        if(this.databaseInObject != true){
          if (this.isYML == true) {
           return new Map(YAML.parse(readFileSync(this.path, 'utf8')))
           } else {
           return new Map(JSON.parse(readFileSync(this.path)))
           }
        } else {
          if (this.isYML == true) {
            return new Map(Object.entries(YAML.parse(readFileSync(this.path, 'utf8'))))
           } else {
            return new Map(Object.entries(JSON.parse(readFileSync(this.path))))
           }
        }
      } catch (e) {
        return new Map()
      }
    }
     /**
     * @returns {}
     */
     toJSON() {
      return Object.fromEntries(this.cacheGet())
     }
    /**
    * @example db.overwrite(`data`)
    * @param data
    * @returns 
    */
    overwrite(data) {
      this.cache = new Map(Array.from(data))
      if(this.databaseInObject != true){
        if (this.isYML != true) {
          writeFileSync(this.path, JSON.stringify(Array.from(data), null, 4))
        } else {
          writeFileSync(this.path, YAML.stringify(Array.from(data)))
        }
      } else {
        if (this.isYML != true) {
          writeFileSync(this.path, JSON.stringify(Object.fromEntries(data), null, 4))
        } else {
          writeFileSync(this.path, YAML.stringify(Object.fromEntries(data)))
        }
      }
    }
    /**
     * @exemple db.put({key:'Profile',value:"Shuruhatik#0001"})
     * @param {string} key 
     * @param {*} value 
     * @returns 
     */
    put(key, value) {
      if (key.value) value = key.value
      if (key.key) key = key.key;
      if (!key) {
        return debug("You must enter a key to search in the data", 1);
      }
      if (!value && value != 0) {
        return debug("You must enter a value to write in the data", 1)
      }
      let cache = this.cacheGet().set(key, this.encryption == true ? this.encryptString(value) : value)
      this.event.emit('addElement', {
        key, value, data: this, cache
      })
      this.overwrite(cache);
      if (this.debug === true) {
        return debug("A new item has been added with a key of " + key + " and a value of " + value, 2)
      }
    }
    /**
     * @exemple db.put({key:'Profile',value:"Shuruhatik#0001"})
     * @param {string} key 
     * @param {*} value 
     * @returns 
     */
    set(key, value) {
      if (Array.isArray(key) == true) {
        key.forEach((item) => {
          this.put(item.key, item.value)
        })
      } else {
        if (key.value) value = key.value
        if (key.key) key = key.key;
        this.put(key, value)
      }
    }
    /**
    * @param {string} key
    * @returns {string} value
    */
    get(key,analysis) {
      if (key.analysis) analysis = key.analysis
      if (key.key) key = key.key
      if (!key) {
        return debug("You must enter a key to search in the data", 1);
      }
      let value = this.cacheGet().get(key)
      this.event.emit('getElement', {key, value})
      return analysis == false ? value : this.analysis(value)
    }
    /**
    * @param {string} key
    * @returns {string} value
    */
    fetch(key) {
      return this.get(key)
    }
    /**
     * @example db.delete({key:`st.db`})
     * @param {string} key
     * @returns {*}
     */
    delete(key) {
      if (key.key) key = key.key
      if (!key) {
        return debug("You must enter a key to search in the data", 1);
      }
      if (this.has({ key }) == false) return false;
      let cache = this.cacheGet();
      cache.delete(key)
      this.overwrite(cache)
      return true
    }
    /**
     * @example db.remove({key:`st.db`})
     * @param {string} key
     * @returns {*}
     */
    remove(key) {
      return this.delete(key)
    }
    /**
    * @param {string} key
    * @returns {string} value
    */
    has(key) {
      if (key.key) key = key.key
      if (!key) {
        return debug("You must enter a key to search in the data", 1);
      }
      return this.cacheGet().has(key)
    }
    /**
    * @example db.fileSize()
    * @param type
    * @returns 
    */
    fileSize() {
      var stats = statSync(`${this.path}`)
      stats.size = {
        byte: stats.size,
        megaBytes: stats.size / (1024 * 1024),
        kiloBytes: stats.size / (1024)
      };
      return stats
    }
    /**
     * @param {string} value 
     * @returns {string}
     */
    encryptString(value) {
      return this.crypto.encryptString(JSON.stringify(value), this.encryptionPassword);
    }
    /**
     * @param {string} value
     * @returns {string}
     */
    decryptString(value) {
      try {
        let vecrypt = this.crypto.decryptString(value, this.encryptionPassword);
        return JSON.parse(vecrypt)
      } catch{
        return value
      }
    }
    /**
    * @param {string} value
    * @returns {string}
    */
    analysis(value) {
      if (this.crypto.isEncrypt(value, this.encryptionPassword) == true) {
        let valueEnd = this.decryptString(value)
        if (this.isNumeric(valueEnd) == true) {
          return +valueEnd
        } else {
          return valueEnd
        }
      } else {
        if (this.isNumeric(value) == true) {
          return +value
        } else {
          return value
        }
      }
    }
    /**
     * @example db.all()
     * @param {number} limit
     * @returns {Object}
     */
    all(limit = 0,analysis) {
      if (limit.analysis) analysis = limit.analysis
      if (typeof limit != "number") limit = limit.limit
      let cache = Array.from(this.cacheGet())
      let arr = []
      cache.forEach((key, v) => {
        let data = this.has(key[0]) ? this.get(key[0],analysis) : key[1]
        arr.push({
          ID: key[0],
          data,
          typeof: typeof data,
          _v: v
        });
      })
      return limit > 0 ? arr.splice(0, limit) : arr
    }
    /**
    * Does a math calculation and stores the value in the database!
    * @param {string} key Data key
    * @param {string} operator One of +, -, %, * or /
    * @param {number} value The value, must be a number
    * @param {boolen} goToNegative Move to negative
    * @return 
    * @example db.math({key:"points",operator:"+",value:150})
    */
    math(key, operator, value, goToNegative) {
      if (key.goToNegative) goToNegative = key.goToNegative
      if (key.operator) operator = key.operator
      if (key.value) value = key.value
      if (key.key) key = key.key;
      let oldvalue = this.has({ key }) == true ? this.get({ key }) : 0
      if (!key) {
        return debug("You must enter a key to search in the data", 1);
      } else if (!operator) {
        return debug("Please type the operator you want to work with", 1)
      } else if (!value) {
        return debug("You must enter a value to write in the data", 1)
      }
      if (this.has({ key }) == false) {
        this.put({ key, value })
        return;
      }
      if (this.isNumeric(value) == false) {
        return debug("The type of value is not a number.", 1);
      } else {
        value = +value
      }
      if (value <= 0) {
        return debug("Value cannot be less than 1", 1);
      }
      if (goToNegative == false && oldvalue < 1) {
        oldvalue = 0;
      }

      switch (operator) {
        case 'add':
        case '+':
          return this.put(key, oldvalue + value);
          break;

        case 'subtract':
        case 'sub':
        case '-':
          return this.put(key, oldvalue - value);
          break;

        case 'multiply':
        case 'mul':
        case '*':
          return this.put(key, oldvalue * value);
          break;

        case 'divide':
        case 'div':
        case '/':
          return this.put(key, oldvalue / value);
          break;

        case 'modulus':
        case '%':
          return this.put(key, oldvalue / value);
          break;
        default:
          debug("Unknown operator provided!", 1);
      }
    }
    /**
    * Returns database connection uptime!
    * @return {Promise<Number>}
    * @example console.log(`Database is up for ${db.uptime} ms.`);
    */
    uptime() {
      if (!this.readyInDate) {
        return 0
      } else {
        return Date.now() - this.readyInDate.getTime();
      }
    }
    /**
     * @example db.add({key:`data`,value:2)
     * @param key
     * @param value
     * @returns 
     */
    add(key, value) {
      if (key.value) value = key.value
      if (key.key) key = key.key;
      if (!key) {
        return debug("You must enter a key to search in the data", 1);
      }
      if (!value) {
        return debug("You must enter a value to write in the data", 1)
      }
      return this.math(key, "+", value);
    }
    /**
     * @example db.multiply({key:"coins", value:2})
     * @param key
     * @param value
     * @returns 
     */
    multiply(key, value) {
      if (key.value) value = key.value
      if (key.key) key = key.key;
      if (!key) {
        return debug("You must enter a key to search in the data", 1);
      }
      if (!value) {
        return debug("You must enter a value to write in the data", 1)
      }
      return this.math(key, "*", value);
    }
    /**
     * @example db.double({key:"coins"})
     * @param key
     * @returns 
     */
    double(key) {
      if (key.value) value = key.value
      if (key.key) key = key.key;
      if (!key) {
        return debug("You must enter a key to search in the data", 1);
      }
      if (!value) {
        return debug("You must enter a value to write in the data", 1)
      }
      return this.math(key, "*", 2);
    }
    /**
     * @example db.subtract({key:"coins", value:50})
     * @param data
     * @returns 
     */
    subtract(key, value) {
      if (key.value) value = key.value
      if (key.key) key = key.key;
      if (!key) {
        return debug("You must enter a key to search in the data", 1);
      }
      if (!value) {
        return debug("You must enter a value to write in the data", 1)
      }
      return this.math(key, "-", value);
    }
    /**
     * @example db.valuesAll()
     * @returns 
     */
    valuesAll() {
      const all = this.all();
      return all.map((element) => element.data);
    }
    /**
     * @example db.keysAll()
     * @returns 
     */
    keysAll() {
      const all = this.all();
      return all.map((element) => element.ID);
    }
    /**
     * @example db.push({key:`hello`,value:2021})
     * @param key
     * @param value
     * @returns 
     */
    push(key, value) {
      if (key.value) value = key.value
      if (key.key) key = key.key;
      if (!key) {
        return debug("You must enter a key to search in the data", 1);
      }
      if (!value) {
        return debug("You must enter a value to write in the data", 1)
      }
      if (this.has(key) == false) {
        return this.set(key, [value]);
      }
      let data = this.get(key)
      if (Array.isArray(data)) {
        data.push(value);
        return this.set(key, data);
      } else {
        return this.set(key, [value]);
      }
    }
    /**
     * @example db.unshift({key:`hello`,value:2021})
     * @param key
     * @param value
     * @returns 
     */
    unshift(key, value) {
      if (key.value) value = key.value
      if (key.key) key = key.key;
      if (!key) {
        return debug("You must enter a key to search in the data", 1);
      }
      if (!value) {
        return debug("You must enter a value to write in the data", 1)
      }
      if (this.has(key) == false) {
        return this.set(key, [value]);
      }
      let data = this.get(key)
      if (Array.isArray(data)) {
        data.unshift(value);
        return this.set(key, data);
      } else {
        return this.set(key, [value]);
      }
    }
    /**
     * @example db.pop({key:`hello`})
     * @param key
     * @returns 
     */
    pop(key) {
      if (key.key) key = key.key;
      if (!key) {
        return debug("You must enter a key to search in the data", 1);
      }
      if (this.has(key) == false) {
        return this.set(key, []);
      }
      let data = this.get(key)
      if (Array.isArray(data)) {
        data.pop();
        return this.set(key, data);
      } else {
        return this.set(key, []);
      }
    }
    /**
     * @example db.shift({key:`hello`})
     * @param key
     * @returns 
     */
    shift(key) {
      if (key.key) key = key.key;
      if (!key) {
        return debug("You must enter a key to search in the data", 1);
      }
      if (this.has(key) == false) {
        return this.set(key, []);
      }
      let data = this.get(key)
      if (Array.isArray(data)) {
        data.shift();
        return this.set(key, data);
      } else {
        return this.set(key, []);
      }
    }
    /**
     * @example db.info()
     * @returns 
     */
    info() {
      return {
        uptimeMillSeconds: this.uptime(),
        fileSize: this.fileSize(),
        version: "v3.1.0",
        size: this.size
      };
    }
    /**
     * @example db.unpush({key:`hello`,value:2021})
     * @param db
     * @param data
     * @returns 
     */
    unpush(key, value) {
      if (key.value) value = key.value
      if (key.key) key = key.key;
      if (!key) {
        return debug("You must enter a key to search in the data", 1);
      }
      if (!value) {
        return debug("You must enter a value to unpush in the data", 1)
      }
      if (this.has({ key }) == false) {
        return debug("There is no data to execute on it", 1)
      } else {
        this.set(key, this.get({ key }).filter((x) => x !== value));
      }
    }
    /**
     * @example db.importFromQuickDB(quickDB)
     * @param {*} quickDB
     * @returns 
     */
    importFromQuickDB(quickDB) {
      if (!quickDB) {
        return debug("Please write value to quick db", 1)
      }
      quickDB.all().forEach(async body => {
        this.set(body.ID, quickDB.get(body.ID))
      })
      if (this.debug === true) {
        debug("Copied successfully", 2)
      }
    }
    /**
     * @example db.transferDB({path:fileDB})
     * @param {*} fileDB
     * @returns 
     */
    transferDB(db) {
      if (db.path) db = db.path
      let fileData = db.cache()
      let localData = this.cacheGet()
      db.overwrite(localData)
      this.overwrite(fileData)

      if (this.debug === true) {
        debug("Transfer successfully", 2)
      }
    }
    /**
     * @example db.type({key:`st.db`})
     * @param key
     * @returns 
     */
    type(key) {
      if (key.key) key = key.key;
      if (!key) {
        return debug("You must enter a key to search in the data", 1);
      }
      const data = this.get(key);
      if (Array.isArray(data) || data instanceof Array) {
        return "array";
      } else {
        return typeof data;
      }
    }
    /**
     * @example db.destroy()
     * @returns 
     */
    destroy() {
      try {
        unlinkSync(`./${this.dbName}`)
      } catch (err) {
        debug("The data has been destroyed before!", 1)
      }
      return;
    }
    /**
     * @example db.startsWith({key:`st.db`})
     * @param key
     * @returns 
     */
    startsWith(key) {
      if (key.key) key = key.key
      return this.filter((element) => element.ID.startsWith(key));
    }
    /**
     * @example db.endsWith({key:`st.db`})
     * @param key
     * @returns 
     */
    endsWith(key) {
      if (key.key) key = key.key
      return this.filter((element) => element.ID.endsWith(key));
    }
    /**
     * @example {*} db.filter()
     * @param {*} arg
     * @param {*} callback
     * @returns 
     */
    filter(callback, arg) {
      if (arg) callback = callback.bind(arg);
      return this.all().filter(callback);
    }
    /**
     * @example db.includes({key:`st.db`})
     * @param {*} key
     * @returns 
     */
    includes(key) {
      if (key.key) key = key.key
      return this.filter((element) => element.ID.includes(key));
    }
    /**
     * @example db.fetchAll()
     * @param key
     * @returns 
     */
    fetchAll() {
      return this.all()
    }
    /**
     * @example db.clear()
     * @returns 
     */
    clear() {
      this.overwrite(new Map())
    }
    /**
     * @example db.reload()
     * @returns 
     */
    reload() {
      let data = this.cacheGet()
      this.clear()
      setTimeout(() => this.overwrite(data), 200)
    }
    /**
     * @example db.OldToNewDatabase("./old.json")
     * @param {string} oldPath
     * @returns {*}
     */
    OldToNewDatabase(oldPath) {
      let oldtype = oldPath.endsWith("yml") ? "yml"  : oldPath.endsWith("yaml") ? "yaml" : "json"
      try {
        if (this.isYML == true) {
          return new Map(Object.entries(YAML.parse(readFileSync(pathResolve(oldPath, oldtype), 'utf8'))))
        } else {
          return new Map(Object.entries(JSON.parse(readFileSync(pathResolve(oldPath, oldtype)))))
        }
      } catch (e) {
        return new Map()
      }
    }
  }


  module.exports = Database
}).call(this);