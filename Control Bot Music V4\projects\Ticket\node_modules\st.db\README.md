

<div  align="center">
<p>
<img style="margin-bottom:-6px" src="https://i.imgur.com/kndmTo4.png"/>
<a href="https://www.npmjs.com/package/st.db/" alt="st.db" /></a>
</p>
<p>
 <a href="https://dsc.gg/shuruhatik"><img src="https://img.shields.io/discord/766364402763956254?color=5865F2&label=ST%20Studio&logo=discord&logoColor=FFFFFF" alt="Discord server" /></a> <a href="https://www.npmjs.com/package/st.db"> <img src="https://badgen.net/npm/node/st.db" alt="Size" /></a> <a href="https://www.npmjs.com/package/st.db"><img src="https://img.shields.io/npm/dt/st.db.svg?maxAge=3600" alt="NPM downloads" /></a> <a href="https://www.npmjs.com/package/st.db"><img src="https://img.shields.io/npm/v/st.db.svg?maxAge=3600?color=5865F2" alt="NPM version" /></a> <a href="https://www.npmjs.com/package/st.db"><img src="https://img.shields.io/npm/l/st.db" alt="license" /></a>
</p>
</div>

## What is ST.db?
- It helps you to store files __inside__ the project or __outside__ the project in **YML** and **JSON** formats in shapes such as `objects` or `maps`

# Features
- It contains valuable and useful methods
- Strong intelligence in reading, recording and analyzing data
- Simple and Easy To get started
- You can store the data in the form of an Object or a Map, as you wish
- Multiple JSON or YAML Files
- You can store, write and read JSON and YAML files outside the project
- There are more than one mode and you can switch between them
- can I switch data from file to file
- Supports data encryption mode which enables you to encrypt data
- Supports data encryption mode if you want it
- Methods are strict
- Supports moving Quick.DB data to ST.db data file
- System for reading data from one project to another
- Increase in performance and increase in reading and writing
- Supports filters
- Data encryption with custom password

## Content
- [Installing](#installation)
- [What is ST.db?](#what-is-stdb)
   - [Important Notes](#important-notes)
- [Features](#features)
  - [Important Notes](#important-notes)
  - [Encryption mode](#how-do-i-turn-on-data-encryption-mode)
     - [Notes for encryption mode](#notes-for-encryption-mode)
  - [Moving Quick.DB data to ST.db data file](#moving-quickdb-data-to-stdb-data-file)
  - [Debug Mode](#debug-mode)
  - [Multiple Files](#multiple-files)
  - [Store data as objects](#store-data-as-objects)
  - [Database in files from outside the work project](#database-in-files-from-outside-the-work-project)
  - [Switch data from file to file](#how-can-i-switch-data-from-file-to-file-)
  - [transfer data from file to file](#how-to-transfer-data-from-file-to-file-)
  - [Data reading system in more than one place](#data-reading-system-in-more-than-one-place)
    - [How can I read data in another project?](#how-can-i-read-data-in-another-project)
 - [Events](#events)
    - [Data connection event](#data-connection-event)
    - [Add an value in the data](#add-an-value-in-the-data)
    - [Read a value by Element](#read-a-value-by-element)
 - [Methods](#sets-a-value-to-the-specified-key-on-the-database)
    - [Sets a value to the specified key on the database](#sets-a-value-to-the-specified-key-on-the-database)
    - [Fetches the data from database!](#fetches-the-data-from-database)
    - [Fetches everything and sorts by given target](#fetches-everything-and-sorts-by-given-target)
    - [Set, get, delete and control the array in the database](#set-get-delete-and-control-the-array-in-the-database)
    - [Deletes a key from the database!](#deletes-a-key-from-the-database)
    - [Delete's all of the data from the database!](#deletes-all-of-the-data-from-the-database)
    - [Returns everything from the database](#returns-everything-from-the-database)
    - [Does a math calculation and stores the value in the database!](#does-a-math-calculation-and-stores-the-value-in-the-database)
    - [Checks if there is a data stored with the given key](#checks-if-there-is-a-data-stored-with-the-given-key)
    - [Convert old ST.db from object to map](#convert-old-stdb-from-object-to-map)
    - [Return's the value type of the key!](#returns-the-value-type-of-the-key)
    - [Convert all data to JSON](#convert-all-data-to-json)
    - [Find out the size of the database file](#find-out-the-size-of-the-database-file)
    - [Encrypt and decrypt a value of the same type](#encrypt-and-decrypt-a-value-of-the-same-type)
    - [Reload the data in the database](#reload-the-data-in-the-database)
- [Example usage](#example-usage)
    - [discord.js](#example-usage)
    - [eris](#example-bot-with-eris)

#  Getting Started
## Installation
You can start install the package on your project:
```sh-session
npm install st.db
yarn add st.db
pnpm add st.db
```
-   CommonJS
```js
const Database = require('st.db')
```
-   ESM
```js
import Database from 'st.db';
```
Then Start define it like this:
- Example :`JSON`

![encrypt](  
https://imgur.com/k2JSqeL.png)

```js
const db = new Database({path:'FileName.json'})//You can write the name you want
```

- Example :`YAML`

![encrypt](  
https://imgur.com/yvVoaSp.png)

```js
const db = new Database({path:'FileName.yml'})//You can write the name you want
```

## What are the features of maps in the database?
- A  `Map`  does not contain any keys by default. It only contains what is explicitly put into it.
- A  `Map`'s keys can be any value (including functions, objects, or any primitive).
- The keys in  `Map`  are ordered in a simple, straightforward way: A  `Map`  object iterates entries, keys, and values in the order of entry insertion.
- Performs better in scenarios involving frequent additions and removals of key-value pairs.
- A  `Map`  is an  [iterable](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols), so it can be directly iterated.

## Important Notes
- Note that you can write the file extension or you can write the file name without the extension and in both cases it will be recognized from the data easily
- If the file dosen't exist, it will create it
- In the event that you store more than 50 objects per millisecond, it is recommended to use `await` before any action
   - Example to prevent pressure, if there is pressure or without pressure, you can use it
```js
// set
await db.set({ key:'Array', value:['eg',  'ps']  });

// get
console.log(await db.get({key:'Array'}))
```

## How do I turn on data encryption mode?
- Now your data is encrypted in ST.db
![encrypt](  
 https://imgur.com/Vm5XZLn.png)
- An example of a method for operating an encryption mode and reading encrypted data
```js
const Database = require('st.db')
const db = new Database({
  path:'FileName.json',
  encryption: {
    password:"shuruhatik"
    //digest:"sha512"// one of: 'blake2b512' | 'blake2s256' | 'md4' | 'md5' | 'md5-sha1' | 'mdc2' | 'ripemd160' | 'sha1' | 'sha224' | 'sha256' | 'sha3-224' | 'sha3-256' | 'sha3-384' | 'sha3-512' | 'sha384' | 'sha512' | 'sha512-224' | 'sha512-256' | 'sm3' | 'whirlpool';
  }
})
// or

const db = new Database({
  path:'FileName.json',
  encryption: true
})
```
## Notes for encryption mode
- Note in the event that you do not activate the encryption feature, the data will be recorded directly unencrypted, and when you activate the encryption mode, the data will be encrypted when recording
- `password` It is an important thing in the event that you forgot the password or changed the password, the data recorded with this password could not be read and you must set the correct password
- Supports object and array encrypting now
- If you do not enter a specific password, the default password will be `st.db`

## Moving Quick.DB data to ST.db data file

```js
const Database = require('st.db')
const db = new Database({path:'FileName.yml'})
const quickdb = require("quick.db");

db.importFromQuickDB(quickdb)
```
- `Note!` The sql format is converted to json format so that it can be fully stored and used in our data

## Debug Mode
- To know in console everything is added to the data
```js
const Database = require('st.db')
const db = new Database({
   path:'FileName.json',
   debug: true
})
```
![debug](  
https://imgur.com/w7SrJjz.png)

## Store data as objects
- If you want to store data in the form of objects, but note that if this option "databaseInObject" is not enabled, then the normal situation for data is that it is stored in the form of MAPS because it contains more features, but if you want to store in the form of objects, there is no objection
```js
import Database from 'st.db';
const db = new Database({
  path:'FileName.json',
  databaseInObject: true//This option must be enabled if you want to store as objects
})
```
## Multiple Files
- Example 
```javascript
import Database from 'st.db';

const bot = new Database({path:'bot.json'});
const servers = new Database({path:'servers.json'});
const users = new Database({path:'users.json'});

servers.set({
  key: 'guilds',
  value: '800060636041314375'
}); 
bot.set({
  key:'prefix',
  value: '$'
});
users.set({
  key:'blacklist_742070589212327947',
  value: true
}); 
```
## How can I switch data from file to file ?
- Example
```javascript
import Database from 'st.db';

const guilds = new Database({path:'guilds-data'});
const users = new Database({path:'users-data'});

// switch data from users-data to guilds-data
guilds.transferDB(users)
```

## How to transfer data from file to file ?
- Example
```javascript
import Database from 'st.db';

const guilds = new Database({path:'guilds-data.json'});
const users = new Database({path:'users-data.json'});

// Data is transferred from users to guilds
guilds.overwrite(users.load())
```

## Data reading system in more than one place
- API mode
```js
import Database from 'st.db';
const db = new Database({
  path:'FileName.json',
  API: true//If you want to create an API for your data, normal mode it is not enabled
})
```
- The data you record is stored in your own api, and the api is like that 
```
http://localhost/Filename.json || http://localhost/Filename.yml
```
- `localhost` Type in the URL for your project
- `Filename` Type the filename without the folder extension and without the json syntax
```js
https://YouName.ProjectName.repl.co/Filename.json || https://YouName.ProjectName.repl.co/Filename.yml
```
- Here is an example URL for `repl it`
### How can I read data in another project?
- Very simply, you are now data that has become an api, which you can read what is in it using the `node-fetch` or `got` package

## Database in files from outside the work project
- This option is in the event that you want to store or read in a file inside a hard disk other than the one you are using or even outside the folder you are running, so you can in ST.db control it in a file outside your main project if you want
-  If you activate the "pathOutsideTheProject" option, you will be able to type any path from your computer in the "path" option.
```js
const db = new Data({
  path:"C:/Users/<USER>/Desktop/database.yml",
  pathOutsideTheProject:true//You must activate this option in order for the volume to understand that you want to store this path while it is outside the project
})
```

# Events
- Events help you know the value of something when a specific event occurs
### Data connection event
#### Parameters:
| Name |Type|Description|
| ------------ | ------------ |------------ |
| `value`  |object| Information about your Database settings.  |

#### Examples
```js
db.on('isReady', (database) =>{
   console.log(`The data is working successfully`)
   console.log(database)
})
```
### Add an value in the data
#### Parameters:
| Name |Type|Description|
| ------------ | ------------ |------------ |
| `value`  |object| Information about the added value.  |

#### Examples
```js
db.on('addElement', (data) =>{
   console.log(`Key => ${data.key}`) // Key => test
   console.log(`Value => ${data.value}`) // Value => true
})

db.set({key:'test',value: true})
```
### Read a value by Element
#### Parameters:
| Name |Type|Description|
| ------------ | ------------ |------------ |
| `value`  |object| Information about the value that was read.  |

#### Examples
```js
db.on('getElement', (data) =>{
   console.log(`Key => ${data.key}`) // Key => test
   console.log(`Value => ${data.value}`) // Value => true
})

db.get('test')
```


## Sets a value to the specified key on the database!
#### Parameters:

| Name |Type|Description|
| ------------ | ------------ |------------ |
| `key`  | string  | The key to set.  |
| `value`  |all types| The value to set on the key.  |

#### Examples
```js
db.set({
    key:"age",value:16
})

// or

db.set({
    key:{id:"742070589212327947",uuid:"a4s7-4qw7-rq84-5fsd"},value:16
})

// or

db.set([
   {
      key:"age",value:16
   },
   {
      key:"name",value:"mohamed"
   }
])
```

## Fetches the data from database!
#### Parameters:
| Name |Type|Description|
| ------------ | ------------ |------------ |
| `key`  | string  | Key  |
#### Returns:
-   Type:  <("[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)"|"[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)"|"[bigint](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/BigInt)"|"[boolean](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean)"|"[symbol](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Symbol)"|"[undefined](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Undefined)"|"[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)"|"[Function](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function)"|"[array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array)")>
#### Examples
```js
db.get({key:'profile'}); // Get the value of the data
db.fetch({key:'data'}); // Fetches the value of the data
```
## Fetches everything and sorts by given target
#### Parameters:
| Name |Type|Description|
| ------------ | ------------ |------------ |
| `key`  | string  | The key to set.  |
#### Returns:
-   Type:  <("[array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array)")>
#### Examples
```js
db.includes({key:"tes"});// It fetches the values ​​containing this value
db.startsWith({key:"te"});// It fetches values ​​starting with this value
db.endsWith({key:"st"});// It fetches values ​​ending with this value
```

## Set, get, delete and control the array in the database
#### Parameters:
| Name |Type|Description|
| ------------ | ------------ |------------ |
| `key`  | string  | The key to set.  |
| `value`  |    | Value to push.  |

#### Examples
```js
//"hello":[2020]

//It sets at the end
db.push({
  key:`hello`,
  value:2021
})// "hello":[2020,2021] 

//Iteratively deletes the value from the array
db.unpush({
  key:`hello`,
  value:2020
})// "hello":[2021]

//It sets at the start
db.unshift({
  key:`hello`,
  value:2019
})//"hello":[2019,2020,2021]

//It removes the first value from the array
db.shift({
  key:`hello`
})//"hello":[2020,2021]

//It removes the last value from the array
db.pop({
  key:`hello`
})//"hello":[2019,2020]
```

## Deletes a key from the database!
#### Parameters:
| Name |Type|Description|
| ------------ | ------------ |------------ |
| `key`  | string  | The key to delete.  |

#### Examples
```js
 // Removing something from an array using value/index
db.remove({key:'Array'});
//or
db.delete({key:'Array'});
```

## Delete's all of the data from the database!
#### Parameters:
| Name |Type|Description|
| ------------ | ------------ |------------ |
| ``ops``  | object | Clear options.  |

#### Examples
```js
db.clear(); // Clears everything from the database
db.destroy(); // Delete the database file (And Clear All Data)
```

## Returns everything from the database
#### Parameters:
| Name |Type|Description|
| ------------ | ------------ |------------ |
| `limit`  | number | Define a limit on the values ​​that reads  |
| `ops` |   | All options  |
#### Returns:
-   Type:  <("[array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array)")>
#### Examples
```js
//Returns everything from the database
db.all(); || db.fetchAll()
db.all(5); || db.fetchAll(5)//You can select the number you want to read
/*
 => Example
[
  { ID: 'coins', data: 12, typeof: 'number', _v: 0 },
  { ID: 'name', data: 'Shuruhatik', typeof: 'string', _v: 1 }
]
*/

//Return everything from the database and decrypt the data that is required if you are using an encryption mode
db.decryptAll()

//Return all values from the database
db.valuesAll()

//Return all keys from the database
db.keysAll()
```
## Does a math calculation and stores the value in the database!
#### Parameters:
| Name |Type|Description|
| ------------ | ------------ |------------ |
| `key`  | string | The key to set.  |
| `operator`  |string|One of +, -, %, * or /|
| `value`  | number|The value, must be a number.  |

#### Examples
```js
db.math({
  key:"coins",  
  operator:"+",  
  value:"100", 
  goToNegative:false
})

// To subtract from value
db.subtract({key:"coins", value:50})

// To add from value
db.add({key:"coins", value:50})

// To multiply from value
db.multiply({key:"coins", value:2})

//To double from value
db.double({key:"coins"})
```
## Checks if there is a data stored with the given key

#### Parameters:
| Name |Type|Description|
| ------------ | ------------ |------------ |
| `key`  | string | Key.  |
#### Returns:
-   Type: <[Boolean](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean)>
#### Examples
```js
db.has({key:"coins"})//Returns "true" or "false" if the database has the data or not.
```
## Convert old ST.db from object to map
#### Parameters:
| Name |Type|Description|
| ------------ | ------------ |------------ |
| `key`  | string | Key.  |
#### Returns:
-   Type: <[Map](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Map))>
#### Examples
```js
console.log(db.OldToNewDatabase("./user.json"))
/*
Example return :
 Map {
    user' => {
      id: [ '45451' ],
      name: [ 'Mohamed' ],
      admin: [ 'true' ]
   }
 }
*/

//In case you want to set old items in st.db maps
db.overwrite(db.OldToNewDatabase("user.json"))
```
## Return's the value type of the key!

#### Parameters:
| Name |Type|Description|
| ------------ | ------------ |------------ |
| `key`  | string | Key.  |
#### Returns:
-   Type:  <("[string](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String)"|"[number](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number)"|"[bigint](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/BigInt)"|"[boolean](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Boolean)"|"[symbol](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Symbol)"|"[undefined](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Undefined)"|"[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)"|"[Function](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function)"|"[array](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array)")>
#### Examples
```js
db.type({key:"coins"})//To find out the type of value written in the data
```
## Convert all data to JSON
#### Returns:
-   Type:  <("[object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object)")>
#### Example
```js
db.toJSON()
```
## Find out the size of the database file
#### Example
```js
console.log(db.fileSize())//To know the file size and details about it
console.log(db.size) // If you want to know the number of registered items
/*
Example
=> { byte: 36, megaBytes: 0.000034332275390625, kiloBytes: 0.03515625 }
*/
```
## Reload the data in the database
```js
db.reload()
```

## Encrypt and decrypt a value of the same type
#### Parameters:
| Name |Type|Description|
| ------------ | ------------ |------------ |
| `key`  | string  | Key  |

#### Examples
```js
db.encryptString(`st.db`); // To encrypt a desired value
db.decryptString('83b3031fedd8774b37b5745774be8d1b:744242457637733d'); // To decrypt a desired value
```

# Example usage
## Example Bot With `Discord.js v13`
```js
const Database = require('st.db');
const { Client, Intents } = require('discord.js');
const client = new Client({ intents: [Intents.FLAGS.GUILDS] });
const db = new Database({path:'FileName.yml'});
client.db = db;

client.on('ready', () => {
  console.log(`Database size is => ${client.db.fileSize().byte} byte!`);
  client.db.set({key:client.user,value:"Ready to use"})
});

client.login('token');
```
## Example Bot With `Eris`
```js
const Database = require('st.db');
const Eris = require("eris");
const bot = new Eris("Bot TOKEN");
const db = new Database({path:'FileName.yml'});

bot.on('ready', () => {
  console.log(`Database size is => ${bot.db.fileSize().byte} byte!`);
  db.set({key:bot.user,value:"Ready to use"})
});

bot.connect(); 
```

## Contact
**Any bug or suggestion !**
 - Contact With Me Discord: [`Shuruhatik#2443`](https://github.com/shuruhatik)
### Server Support
<a href="https://dsc.gg/shuruhatik"><img src="https://invidget.switchblade.xyz/uGu2sCDZhv"></a>

## License 
- [CC BY-NC-ND 4.0](https://creativecommons.org/licenses/by-nc-nd/4.0/legalcode)