const https = require('https');
const http = require('http');

/**
 * التحقق من صحة رابط الصورة
 * @param {string} url - رابط الصورة
 * @returns {Promise<boolean>} - true إذا كان الرابط صالح
 */
async function validateImageUrl(url) {
  return new Promise((resolve) => {
    if (!url || (!url.startsWith('http://') && !url.startsWith('https://'))) {
      resolve(false);
      return;
    }

    const protocol = url.startsWith('https://') ? https : http;
    
    const timeout = setTimeout(() => {
      resolve(false);
    }, 5000); // 5 ثوانٍ timeout

    const req = protocol.request(url, { method: 'HEAD' }, (res) => {
      clearTimeout(timeout);
      
      // التحقق من كود الاستجابة
      if (res.statusCode >= 200 && res.statusCode < 300) {
        // التحقق من نوع المحتوى
        const contentType = res.headers['content-type'];
        if (contentType && contentType.startsWith('image/')) {
          resolve(true);
        } else {
          resolve(false);
        }
      } else {
        resolve(false);
      }
    });

    req.on('error', () => {
      clearTimeout(timeout);
      resolve(false);
    });

    req.on('timeout', () => {
      clearTimeout(timeout);
      resolve(false);
    });

    req.setTimeout(5000);
    req.end();
  });
}

/**
 * التحقق من صحة رابط الصورة مع معلومات مفصلة
 * @param {string} url - رابط الصورة
 * @returns {Promise<{valid: boolean, error?: string, contentType?: string}>}
 */
async function validateImageUrlDetailed(url) {
  return new Promise((resolve) => {
    if (!url) {
      resolve({ valid: false, error: 'URL is empty' });
      return;
    }

    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      resolve({ valid: false, error: 'Invalid URL format' });
      return;
    }

    const protocol = url.startsWith('https://') ? https : http;
    
    const timeout = setTimeout(() => {
      resolve({ valid: false, error: 'Request timeout' });
    }, 10000);

    const req = protocol.request(url, { method: 'HEAD' }, (res) => {
      clearTimeout(timeout);
      
      if (res.statusCode >= 200 && res.statusCode < 300) {
        const contentType = res.headers['content-type'];
        if (contentType && contentType.startsWith('image/')) {
          resolve({ 
            valid: true, 
            contentType: contentType,
            size: res.headers['content-length'] 
          });
        } else {
          resolve({ 
            valid: false, 
            error: `Invalid content type: ${contentType}` 
          });
        }
      } else {
        resolve({ 
          valid: false, 
          error: `HTTP ${res.statusCode}: ${res.statusMessage}` 
        });
      }
    });

    req.on('error', (error) => {
      clearTimeout(timeout);
      resolve({ 
        valid: false, 
        error: `Network error: ${error.message}` 
      });
    });

    req.setTimeout(10000);
    req.end();
  });
}

/**
 * قائمة بالامتدادات المدعومة للصور
 */
const supportedImageExtensions = [
  '.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg'
];

/**
 * التحقق من امتداد الملف
 * @param {string} url - رابط الصورة
 * @returns {boolean}
 */
function hasValidImageExtension(url) {
  if (!url) return false;
  
  const urlLower = url.toLowerCase();
  return supportedImageExtensions.some(ext => urlLower.includes(ext));
}

module.exports = {
  validateImageUrl,
  validateImageUrlDetailed,
  hasValidImageExtension,
  supportedImageExtensions
};
