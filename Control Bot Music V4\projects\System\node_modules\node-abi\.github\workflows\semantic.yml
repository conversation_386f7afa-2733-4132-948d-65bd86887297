name: "Check Semantic Commit"

on:
  pull_request:
    types:
      - opened
      - edited
      - synchronize

permissions:
  contents: read

jobs:
  main:
    permissions:
      pull-requests: read  # for amannn/action-semantic-pull-request to analyze PRs
      statuses: write  # for amannn/action-semantic-pull-request to mark status of analyzed PR
    name: Validate PR Title
    runs-on: ubuntu-latest
    steps:
      - name: semantic-pull-request
        uses: amannn/action-semantic-pull-request@c3cd5d1ea3580753008872425915e343e351ab54 # v5.2.0
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          validateSingleCommit: false
