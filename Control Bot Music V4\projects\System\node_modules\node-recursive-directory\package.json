{"name": "node-recursive-directory", "version": "1.2.0", "description": "Npm package to get directory files recursively", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/vvmspace/node-recursive-directory.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/vvmspace/node-recursive-directory/issues"}, "homepage": "https://github.com/vvmspace/node-recursive-directory#readme", "dependencies": {"glob": "^7.1.6"}}