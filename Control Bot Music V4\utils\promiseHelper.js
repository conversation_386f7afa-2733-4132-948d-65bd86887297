/**
 * مساعد للتعامل مع Promise وتجنب multipleResolves warnings
 */

/**
 * إنشاء Promise آمن يتجنب multiple resolves
 * @param {Function} executor - دالة التنفيذ
 * @returns {Promise} Promise آمن
 */
function createSafePromise(executor) {
  return new Promise((resolve, reject) => {
    let settled = false;
    
    const safeResolve = (value) => {
      if (!settled) {
        settled = true;
        resolve(value);
      }
    };
    
    const safeReject = (reason) => {
      if (!settled) {
        settled = true;
        reject(reason);
      }
    };
    
    try {
      executor(safeResolve, safeReject);
    } catch (error) {
      safeReject(error);
    }
  });
}

/**
 * تنفيذ دالة مع timeout آمن
 * @param {Function} fn - الدالة المراد تنفيذها
 * @param {Number} timeout - المهلة الزمنية بالميلي ثانية
 * @returns {Promise} نتيجة التنفيذ أو timeout
 */
function withTimeout(fn, timeout = 30000) {
  return createSafePromise((resolve, reject) => {
    const timeoutId = setTimeout(() => {
      reject(new Error(`Operation timed out after ${timeout}ms`));
    }, timeout);
    
    Promise.resolve(fn())
      .then(result => {
        clearTimeout(timeoutId);
        resolve(result);
      })
      .catch(error => {
        clearTimeout(timeoutId);
        reject(error);
      });
  });
}

/**
 * تنفيذ عدة promises مع تجنب multiple resolves
 * @param {Array} promises - قائمة الـ promises
 * @returns {Promise} نتيجة جميع الـ promises
 */
function safePromiseAll(promises) {
  return createSafePromise((resolve, reject) => {
    Promise.all(promises)
      .then(resolve)
      .catch(reject);
  });
}

/**
 * تنفيذ promise مع إعادة المحاولة
 * @param {Function} fn - الدالة المراد تنفيذها
 * @param {Number} retries - عدد المحاولات
 * @param {Number} delay - التأخير بين المحاولات
 * @returns {Promise} نتيجة التنفيذ
 */
function retryPromise(fn, retries = 3, delay = 1000) {
  return createSafePromise((resolve, reject) => {
    const attempt = (remainingRetries) => {
      Promise.resolve(fn())
        .then(resolve)
        .catch(error => {
          if (remainingRetries > 0) {
            setTimeout(() => attempt(remainingRetries - 1), delay);
          } else {
            reject(error);
          }
        });
    };
    
    attempt(retries);
  });
}

/**
 * تنفيذ promise مع fallback
 * @param {Function} primary - الدالة الأساسية
 * @param {Function} fallback - الدالة البديلة
 * @returns {Promise} نتيجة التنفيذ
 */
function promiseWithFallback(primary, fallback) {
  return createSafePromise((resolve, reject) => {
    Promise.resolve(primary())
      .then(resolve)
      .catch(() => {
        Promise.resolve(fallback())
          .then(resolve)
          .catch(reject);
      });
  });
}

/**
 * تحويل callback إلى promise آمن
 * @param {Function} fn - دالة callback
 * @returns {Function} دالة ترجع promise
 */
function promisify(fn) {
  return (...args) => {
    return createSafePromise((resolve, reject) => {
      fn(...args, (error, result) => {
        if (error) {
          reject(error);
        } else {
          resolve(result);
        }
      });
    });
  };
}

/**
 * تأخير التنفيذ
 * @param {Number} ms - المدة بالميلي ثانية
 * @returns {Promise} promise للتأخير
 */
function delay(ms) {
  return createSafePromise((resolve) => {
    setTimeout(resolve, ms);
  });
}

/**
 * تنفيذ promises بشكل متسلسل
 * @param {Array} tasks - قائمة المهام
 * @returns {Promise} نتائج جميع المهام
 */
function sequentialPromises(tasks) {
  return createSafePromise(async (resolve, reject) => {
    try {
      const results = [];
      for (const task of tasks) {
        const result = await Promise.resolve(task());
        results.push(result);
      }
      resolve(results);
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * تحديد عدد promises المتزامنة
 * @param {Array} tasks - قائمة المهام
 * @param {Number} concurrency - عدد المهام المتزامنة
 * @returns {Promise} نتائج جميع المهام
 */
function limitConcurrency(tasks, concurrency = 3) {
  return createSafePromise(async (resolve, reject) => {
    try {
      const results = [];
      const executing = [];
      
      for (const task of tasks) {
        const promise = Promise.resolve(task()).then(result => {
          results.push(result);
          executing.splice(executing.indexOf(promise), 1);
          return result;
        });
        
        executing.push(promise);
        
        if (executing.length >= concurrency) {
          await Promise.race(executing);
        }
      }
      
      await Promise.all(executing);
      resolve(results);
    } catch (error) {
      reject(error);
    }
  });
}

module.exports = {
  createSafePromise,
  withTimeout,
  safePromiseAll,
  retryPromise,
  promiseWithFallback,
  promisify,
  delay,
  sequentialPromises,
  limitConcurrency
};
